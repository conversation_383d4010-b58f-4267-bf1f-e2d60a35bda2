package com.example.chat.config;

/**
 * OpenAI API 配置类
 */
public class OpenAIConfig {
    
    // OpenAI API 基础 URL
    public static final String OPENAI_API_BASE_URL = "https://api.deepseek.com";
    
    // Chat Completions API 端点
    public static final String CHAT_COMPLETIONS_ENDPOINT = "/chat/completions";
    
    // 默认模型
    public static final String DEFAULT_MODEL = "deepseek-chat";
    
    // 高级模型
    public static final String ADVANCED_MODEL = "gpt-4";
    
    // 最新模型
    public static final String LATEST_MODEL = "gpt-4-turbo-preview";
    
    // 默认参数
    public static final double DEFAULT_TEMPERATURE = 0.7;
    public static final int DEFAULT_MAX_TOKENS = 1000;
    public static final int DEFAULT_TIMEOUT_SECONDS = 30;
    
    // API Key - 从环境变量或系统属性获取
    private static String apiKey;
    
    static {
        // 优先从环境变量获取
        apiKey = System.getenv("OPENAI_API_KEY");
        
        // 如果环境变量没有，尝试从系统属性获取
        if (apiKey == null || apiKey.trim().isEmpty()) {
            apiKey = System.getProperty("openai.api.key");
        }
        
        // 如果还是没有，使用默认值（需要用户手动设置）
        if (apiKey == null || apiKey.trim().isEmpty()) {
            apiKey = "sk-e327dfcea53f415ba3cff6159993f530";
            System.err.println("警告: 未找到 OpenAI API Key！");
            System.err.println("请设置环境变量 OPENAI_API_KEY 或系统属性 openai.api.key");
            System.err.println("例如: export OPENAI_API_KEY=sk-your-key-here");
        }
    }
    
    /**
     * 获取 API Key
     */
    public static String getApiKey() {
        return apiKey;
    }
    
    /**
     * 设置 API Key（用于运行时动态设置）
     */
    public static void setApiKey(String newApiKey) {
        if (newApiKey != null && !newApiKey.trim().isEmpty()) {
            apiKey = newApiKey.trim();
        }
    }
    
    /**
     * 检查 API Key 是否有效
     */
    public static boolean isApiKeyValid() {
        return apiKey != null && 
               !apiKey.trim().isEmpty() && 
               !apiKey.equals("your-openai-api-key-here") &&
               apiKey.startsWith("sk-");
    }
    
    /**
     * 获取完整的 API URL
     */
    public static String getChatCompletionsUrl() {
        return OPENAI_API_BASE_URL + CHAT_COMPLETIONS_ENDPOINT;
    }
}
